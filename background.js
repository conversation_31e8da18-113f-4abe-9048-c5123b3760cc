// استماع للرسائل من content.js و results.js
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  // معالجة طلب تحميل ملف Excel/CSV
  if (request.action === "downloadExcel") {
    // تحديد نوع الملف والامتداد
    const mimeType = 'text/csv;charset=utf-8;';
    const fileExtension = 'csv';

    // إنشاء Blob من البيانات
    const blob = new Blob([request.data], { type: mimeType });

    // إنشاء URL للتحميل
    const url = URL.createObjectURL(blob);

    // تحديد اسم الملف
    let fileName;

    if (request.fileName) {
      // استخدام اسم الملف المحدد من قبل المستخدم
      fileName = request.fileName;
      // التأكد من وجود امتداد الملف
      if (!fileName.toLowerCase().endsWith('.' + fileExtension)) {
        fileName += '.' + fileExtension;
      }
    } else {
      // استخدام اسم ملف افتراضي مع التاريخ
      const date = new Date();
      const dateString = date.getFullYear() + '-' +
                        (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
                        date.getDate().toString().padStart(2, '0') + '_' +
                        date.getHours().toString().padStart(2, '0') + '-' +
                        date.getMinutes().toString().padStart(2, '0');
      fileName = `جدول_منصة_اعتماد_${dateString}.${fileExtension}`;
    }

    // تحميل الملف مباشرة
    try {
      chrome.downloads.download({
        url: url,
        filename: fileName,
        saveAs: false // لا حاجة لعرض مربع حوار التحميل لأن المستخدم قد حدد اسم الملف بالفعل
      }, function(downloadId) {
        // التحقق من نجاح التحميل
        if (chrome.runtime.lastError) {
          console.error('خطأ في التحميل:', chrome.runtime.lastError);
          // محاولة بديلة باستخدام رابط مباشر
          // فتح صفحة تحميل مؤقتة
          chrome.tabs.create({ url: 'download.html', active: false }, function(tab) {
            // إرسال البيانات إلى صفحة التحميل
            setTimeout(() => {
              chrome.tabs.sendMessage(tab.id, {
                action: "directDownload",
                url: url,
                fileName: fileName
              });

              // إغلاق علامة التبويب بعد التحميل
              setTimeout(() => {
                chrome.tabs.remove(tab.id);
              }, 2000);
            }, 500);
          });
        } else {
          // إرسال رسالة بنجاح التحميل
          if (request.tabId) {
            chrome.tabs.sendMessage(request.tabId, { action: "downloadComplete", success: true });
          }
        }
      });
    } catch (error) {
      console.error('خطأ في بدء التحميل:', error);
      // محاولة بديلة باستخدام رابط مباشر
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
    }

    // إذا كان الطلب يتضمن إنهاء الكشط، قم بمسح البيانات وإعادة تعيين الحالة
    if (request.finishScraping) {
      chrome.storage.local.remove(['scrapedData', 'isMultiPageScraping']);
    }
  }

  // معالجة طلب متابعة الكشط من صفحة النتائج
  else if (request.action === "continueScraping") {
    // الحصول على علامة التبويب النشطة
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs.length > 0) {
        // فتح النافذة المنبثقة للإضافة
        chrome.action.openPopup();
      }
    });
  }

  // معالجة اكتمال الكشط التلقائي الجديد
  else if (request.action === "autoScrapeComplete") {
    console.log('🎉 تم استلام إشعار اكتمال الكشط التلقائي');

    const result = request.result;

    if (result.success && result.data && result.data.length > 0) {
      // تخزين البيانات
      chrome.storage.local.set({
        scrapedData: result.data,
        isMultiPageScraping: true,
        autoScrapeCompleted: true
      }, function() {
        console.log('✅ تم حفظ بيانات الكشط التلقائي');

        // فتح صفحة النتائج أو تحديثها
        chrome.storage.local.get(['resultsTabId'], function(storageResult) {
          if (storageResult.resultsTabId) {
            // التحقق من وجود علامة التبويب
            chrome.tabs.get(storageResult.resultsTabId, function(tab) {
              if (tab && !chrome.runtime.lastError) {
                // تحديث علامة التبويب الموجودة
                chrome.tabs.update(storageResult.resultsTabId, { active: true }, function() {
                  chrome.tabs.sendMessage(storageResult.resultsTabId, {
                    action: "updateData",
                    autoScrapeComplete: true,
                    totalPages: result.totalPages,
                    scrapedPages: result.scrapedPages
                  });
                });
              } else {
                // إنشاء علامة تبويب جديدة
                createNewResultsTab();
              }
            });
          } else {
            // إنشاء علامة تبويب جديدة
            createNewResultsTab();
          }
        });

        function createNewResultsTab() {
          chrome.tabs.create({ url: 'results.html' }, function(tab) {
            chrome.storage.local.set({ resultsTabId: tab.id });
          });
        }
      });
    } else {
      console.error('❌ فشل في الكشط التلقائي:', result.error);

      // إرسال إشعار خطأ إلى popup إذا كان مفتوحاً
      chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (tabs.length > 0) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: "autoScrapeError",
            error: result.error
          });
        }
      });
    }
  }

  // معالجة تحديثات التقدم من الكشط التلقائي
  else if (request.action === "autoScrapeProgress") {
    console.log(`📊 تحديث التقدم: الصفحة ${request.currentPage} من ${request.totalPages}`);

    // إرسال التحديث إلى popup إذا كان مفتوحاً
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: "progressUpdate",
          currentPage: request.currentPage,
          totalPages: request.totalPages,
          scrapedPages: request.scrapedPages
        });
      }
    });
  }
});

// استماع لأحداث إغلاق علامة التبويب
chrome.tabs.onRemoved.addListener(function(tabId, removeInfo) {
  // التحقق مما إذا كانت علامة التبويب المغلقة هي صفحة النتائج
  chrome.storage.local.get(['resultsTabId'], function(result) {
    if (result.resultsTabId === tabId) {
      // إذا تم إغلاق صفحة النتائج، قم بمسح معرف علامة التبويب
      chrome.storage.local.remove(['resultsTabId']);
    }
  });
});
