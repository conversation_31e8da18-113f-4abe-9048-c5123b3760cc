<!DOCTYPE html>
<html dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>نتائج كشط الجداول</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      text-align: right;
      background-color: #f5f5f5;
    }
    h1 {
      color: #333;
      margin-bottom: 20px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .page-data {
      margin-bottom: 30px;
      border-bottom: 1px solid #ddd;
      padding-bottom: 20px;
    }
    .page-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #2196F3;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: right;
    }
    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .notes-cell {
      min-width: 150px;
    }
    .notes-input {
      width: 100%;
      padding: 5px;
      border: 1px solid #ddd;
      border-radius: 3px;
      font-family: inherit;
      resize: vertical;
      min-height: 30px;
    }
    .actions {
      margin-top: 20px;
      text-align: center;
    }
    button {
      padding: 10px 20px;
      margin: 0 5px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    .back-button {
      background-color: #2196F3;
    }
    .back-button:hover {
      background-color: #0b7dda;
    }
    .download-button {
      background-color: #ff9800;
    }
    .download-button:hover {
      background-color: #e68a00;
    }
    .no-data {
      text-align: center;
      padding: 50px;
      color: #999;
      font-size: 18px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>نتائج كشط الجداول</h1>

    <div id="results-container">
      <!-- هنا سيتم عرض البيانات المكشوطة -->
      <div class="no-data" id="no-data-message">
        لم يتم كشط أي بيانات بعد.
      </div>
    </div>

    <div class="actions">
      <button id="continue-button" class="back-button">متابعة الكشط</button>
      <button id="download-button" class="download-button">إنهاء وتحميل</button>
    </div>
  </div>

  <script src="results.js"></script>
</body>
</html>
