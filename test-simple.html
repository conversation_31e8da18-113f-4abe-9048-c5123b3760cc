<!DOCTYPE html>
<html dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>اختبار بسيط للإضافة</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      direction: rtl;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: right;
    }
    th {
      background-color: #f2f2f2;
    }
    .instructions {
      background-color: #e7f3ff;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="instructions">
    <h2>🧪 اختبار الإضافة</h2>
    <p><strong>التعليمات:</strong></p>
    <ol>
      <li>انقر على أيقونة الإضافة في شريط الأدوات</li>
      <li>يجب أن ترى قسم "الكشط التلقائي المتقدم"</li>
      <li>افتح Developer Tools (F12) لرؤية الرسائل التشخيصية</li>
      <li>جرب الكشط العادي أولاً</li>
    </ol>
  </div>

  <h1>جدول اختبار بسيط</h1>
  
  <table id="testTable">
    <thead>
      <tr>
        <th>الرقم</th>
        <th>الاسم</th>
        <th>البريد الإلكتروني</th>
        <th>التاريخ</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>1</td>
        <td>أحمد محمد</td>
        <td><EMAIL></td>
        <td>2024-01-01</td>
      </tr>
      <tr>
        <td>2</td>
        <td>فاطمة علي</td>
        <td><EMAIL></td>
        <td>2024-01-02</td>
      </tr>
      <tr>
        <td>3</td>
        <td>محمد حسن</td>
        <td><EMAIL></td>
        <td>2024-01-03</td>
      </tr>
      <tr>
        <td>4</td>
        <td>سارة أحمد</td>
        <td><EMAIL></td>
        <td>2024-01-04</td>
      </tr>
      <tr>
        <td>5</td>
        <td>علي محمود</td>
        <td><EMAIL></td>
        <td>2024-01-05</td>
      </tr>
    </tbody>
  </table>

  <script>
    // إضافة معلومات للمطورين
    console.log('🧪 صفحة اختبار بسيطة جاهزة');
    console.log('📊 عدد الجداول في الصفحة:', document.querySelectorAll('table').length);
    console.log('📊 عدد الصفوف في الجدول:', document.querySelectorAll('#testTable tbody tr').length);
    console.log('🔧 لاختبار الإضافة، افتح الإضافة وجرب الكشط');
    
    // إضافة حدث للنقر على الجدول
    document.getElementById('testTable').addEventListener('click', function() {
      console.log('👆 تم النقر على الجدول');
    });
  </script>
</body>
</html>
