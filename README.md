# كاشط جداول منصة اعتماد - إضافة Chrome محسنة

## التحسينات الجديدة

### 1. اكتشاف ذكي للجداول
- **اكتشاف الجداول التقليدية**: يتعرف على جداول HTML العادية مع `<table>`, `<tr>`, `<td>`
- **اكتشاف الجداول المتجاوبة**: يتعرف على الجداول المبنية بـ `div` والتي تستخدم `data-label` attributes
- **نظام تقييم ذكي**: يختار أفضل جدول في الصفحة بناءً على عوامل متعددة

### 2. دعم data-label attributes
الإضافة الآن تدعم الجداول التي تستخدم `data-label` للعرض المتجاوب مثل:

```html
<tr>
  <td data-label="ID">91</td>
  <td data-label="Branch">فرع حي المصيف - Al-Masif district branch</td>
  <td data-label="Opening Balance">100.00</td>
  <td data-label="Current Cash">13.43</td>
  <td data-label="Network Cash">2.30</td>
  <td data-label="Delivery Cash">0.00</td>
  <td data-label="Total Cash">113.43</td>
  <td data-label="Status">
    <span class="status-badge status-open">
      <i class="fas fa-unlock me-1"></i>
      Open
    </span>
  </td>
  <td data-label="Created By">Ismat Allah</td>
  <td data-label="Created At">27 May 2025</td>
  <td data-label="Closed At">-</td>
  <td data-label="الإجراء">
    <a href="https://quickly24erp.com/branch-cash-management/91" class="btn btn-sm btn-primary">
      <i class="ti ti-eye"></i>
    </a>
  </td>
</tr>
```

### 3. خيارات جديدة في واجهة المستخدم

#### نوع الجدول المطلوب:
- **تلقائي**: كشف جميع أنواع الجداول (افتراضي)
- **جداول HTML تقليدية فقط**: يبحث عن `<table>` فقط
- **جداول متجاوبة مع data-label فقط**: يبحث عن العناصر التي تحتوي على `data-label`

### 4. خوارزمية تقييم الجداول

الإضافة تستخدم نظام نقاط لاختيار أفضل جدول:

#### نقاط الجداول التقليدية:
- 10 نقاط أساسية للجداول التقليدية
- 5 نقاط إضافية للجداول مع class "table"
- 3 نقاط إضافية للجداول مع class "table-bordered"
- 3 نقاط إضافية للجداول مع class "qtable-white"
- نقاط حسب عدد الصفوف والخلايا

#### نقاط الجداول المتجاوبة:
- 8 نقاط أساسية للجداول المبنية بـ div
- نقاط إضافية حسب عدد العناصر مع data-label

#### نقاط إضافية:
- 5 نقاط للجداول العريضة (أكثر من 300px)
- 5 نقاط للجداول الطويلة (أكثر من 200px)
- 10 نقاط للجداول في المحتوى الرئيسي

### 5. تحسينات في استخراج البيانات

#### للجداول التقليدية:
- استخراج الرؤوس من `<thead>`
- دعم `colspan` و `rowspan`
- استخراج البيانات من `<tbody>` و `<tfoot>`

#### للجداول المتجاوبة:
- استخراج الرؤوس من `data-label` attributes
- تجميع العناصر في صفوف بناءً على الحاوي الأب
- دعم النصوص داخل `<span>` elements

## كيفية الاستخدام

1. **تثبيت الإضافة** في Chrome
2. **فتح الصفحة** التي تحتوي على الجدول
3. **النقر على أيقونة الإضافة**
4. **اختيار الخيارات المناسبة**:
   - طريقة تحديد الجدول (تلقائي/يدوي)
   - وضع الكشط (صفحة واحدة/متعدد الصفحات)
   - نوع الجدول المطلوب
5. **النقر على "كشط الجدول"**

## الملفات المحدثة

- `content.js`: تحسينات في اكتشاف واستخراج الجداول
- `popup.html`: إضافة خيارات جديدة
- `popup.js`: دعم الخيارات الجديدة

## المتطلبات

- Chrome Browser
- صفحات تحتوي على جداول HTML أو عناصر مع data-label attributes

## الدعم

الإضافة تدعم الآن:
- ✅ جداول HTML التقليدية
- ✅ جداول متجاوبة مع data-label
- ✅ جداول معقدة مع colspan/rowspan
- ✅ استخراج النصوص من span elements
- ✅ كشط متعدد الصفحات
- ✅ تصدير CSV
