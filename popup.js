document.addEventListener('DOMContentLoaded', function() {
  const scrapeButton = document.getElementById('scrapeButton');
  const selectTableButton = document.getElementById('selectTableButton');
  const openResultsButton = document.getElementById('openResultsButton');
  const statusDiv = document.getElementById('status');
  const autoSelector = document.querySelector('input[name="tableSelector"][value="auto"]');
  const manualSelector = document.querySelector('input[name="tableSelector"][value="manual"]');
  const singleMode = document.querySelector('input[name="scrapeMode"][value="single"]');
  const multiMode = document.querySelector('input[name="scrapeMode"][value="multi"]');
  const tableTypeAuto = document.querySelector('input[name="tableType"][value="auto"]');
  const tableTypeTraditional = document.querySelector('input[name="tableType"][value="traditional"]');
  const tableTypeResponsive = document.querySelector('input[name="tableType"][value="responsive"]');

  // عناصر الكشط التلقائي الجديدة
  const autoScrapeAllButton = document.getElementById('autoScrapeAllButton');
  const autoScrapeProgress = document.getElementById('autoScrapeProgress');
  const autoScrapeControls = document.getElementById('autoScrapeControls');
  const pauseAutoScrape = document.getElementById('pauseAutoScrape');
  const stopAutoScrape = document.getElementById('stopAutoScrape');
  const progressFill = document.querySelector('.progress-fill');
  const progressText = document.querySelector('.progress-text');

  // متغيرات حالة الكشط التلقائي
  let autoScrapeInProgress = false;
  let autoScrapePaused = false;
  let autoScrapeCurrentPage = 1;
  let autoScrapeTotalPages = 0;

  // التحقق من وجود عملية كشط متعددة الصفحات جارية
  chrome.storage.local.get(['scrapedData', 'isMultiPageScraping'], function(result) {
    if (result.isMultiPageScraping && result.scrapedData && result.scrapedData.length > 0) {
      // عرض رسالة للمستخدم
      statusDiv.textContent = 'هناك عملية كشط متعددة الصفحات جارية. يمكنك متابعة الكشط أو إنهاء العملية.';
      statusDiv.className = 'status success';
      statusDiv.style.display = 'block';

      // تغيير نص زر الكشط
      scrapeButton.textContent = 'متابعة الكشط';

      // تحديد وضع الكشط المتعدد
      multiMode.checked = true;
    }
  });

  // فحص إمكانية الكشط التلقائي عند تحميل الصفحة
  checkAutoScrapeAvailability();

  // حل مؤقت: إظهار القسم دائماً للاختبار
  setTimeout(() => {
    console.log('🧪 إظهار قسم الكشط التلقائي للاختبار...');
    const autoScrapeSection = document.querySelector('.auto-scrape-section');
    if (autoScrapeSection) {
      autoScrapeSection.style.display = 'block';
      console.log('✅ تم إظهار قسم الكشط التلقائي');
    } else {
      console.error('❌ لم يتم العثور على قسم الكشط التلقائي');
    }
  }, 1000);

  // تحديث حالة الأزرار عند تغيير طريقة التحديد
  function updateButtonsState() {
    if (manualSelector.checked) {
      selectTableButton.style.display = 'block';
    } else {
      selectTableButton.style.display = 'none';
    }
  }

  // إضافة مستمعي الأحداث لأزرار الاختيار
  autoSelector.addEventListener('change', updateButtonsState);
  manualSelector.addEventListener('change', updateButtonsState);

  // زر تحديد الجدول
  selectTableButton.addEventListener('click', async function() {
    try {
      // الحصول على علامة التبويب النشطة
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // التأكد من تحميل content script
      await ensureContentScriptLoaded(tab.id);

      // تغيير حالة الزر
      selectTableButton.disabled = true;
      selectTableButton.textContent = 'انقر على الجدول في الصفحة...';
      statusDiv.textContent = 'انقر على الجدول الذي تريد كشطه في الصفحة';
      statusDiv.className = 'status success';
      statusDiv.style.display = 'block';

      // إرسال رسالة لتفعيل وضع تحديد الجدول
      chrome.tabs.sendMessage(tab.id, { action: "enableTableSelection" }, function(response) {
        if (chrome.runtime.lastError) {
          // عرض رسالة خطأ
          statusDiv.textContent = 'خطأ في الاتصال: ' + chrome.runtime.lastError.message;
          statusDiv.className = 'status error';
          statusDiv.style.display = 'block';

          // إعادة تفعيل الزر
          selectTableButton.disabled = false;
          selectTableButton.textContent = 'تحديد الجدول';
          return;
        }

        if (response && response.success) {
          // إغلاق النافذة المنبثقة لتمكين المستخدم من النقر على الجدول
          window.close();
        } else {
          // عرض رسالة خطأ
          statusDiv.textContent = 'حدث خطأ أثناء تفعيل وضع التحديد: ' + (response ? response.error : 'لا يوجد استجابة');
          statusDiv.className = 'status error';
          statusDiv.style.display = 'block';

          // إعادة تفعيل الزر
          selectTableButton.disabled = false;
          selectTableButton.textContent = 'تحديد الجدول';
        }
      });
    } catch (error) {
      // عرض رسالة خطأ
      statusDiv.textContent = 'حدث خطأ: ' + error.message;
      statusDiv.className = 'status error';
      statusDiv.style.display = 'block';

      // إعادة تفعيل الزر
      selectTableButton.disabled = false;
      selectTableButton.textContent = 'تحديد الجدول';
    }
  });

  // زر كشط الجدول
  scrapeButton.addEventListener('click', async function() {
    // تغيير حالة الزر
    scrapeButton.disabled = true;
    scrapeButton.textContent = 'جاري الكشط...';

    try {
      // الحصول على علامة التبويب النشطة
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // التأكد من تحميل content script
      await ensureContentScriptLoaded(tab.id);

      // تحديد الخيارات
      const options = {
        selectionMode: autoSelector.checked ? 'auto' : 'manual',
        exportFormat: 'csv',
        multiPage: multiMode.checked,
        tableType: getSelectedTableType()
      };

      // دالة للحصول على نوع الجدول المحدد
      function getSelectedTableType() {
        if (tableTypeTraditional.checked) return 'traditional';
        if (tableTypeResponsive.checked) return 'responsive';
        return 'auto'; // القيمة الافتراضية
      }

      // التحقق من وجود عملية كشط متعددة الصفحات جارية
      chrome.storage.local.get(['scrapedData', 'isMultiPageScraping'], function(result) {
        // إذا كانت هناك عملية كشط متعددة الصفحات جارية، أضف البيانات الموجودة إلى الخيارات
        if (result.isMultiPageScraping && result.scrapedData) {
          options.existingData = result.scrapedData;
          options.continueScraping = true;
        }

        // تنفيذ سكريبت في الصفحة الحالية
        chrome.tabs.sendMessage(tab.id, {
          action: "scrapeTable",
          options: options
        }, function(response) {
          // التحقق من أخطاء الاتصال
          if (chrome.runtime.lastError) {
            console.error('❌ خطأ في الاتصال:', chrome.runtime.lastError);
            statusDiv.textContent = 'خطأ في الاتصال مع الصفحة: ' + chrome.runtime.lastError.message;
            statusDiv.className = 'status error';
            statusDiv.style.display = 'block';

            // إعادة تفعيل الزر
            scrapeButton.disabled = false;
            scrapeButton.textContent = 'كشط الجدول';
            return;
          }

          if (response && response.success) {
            if (options.multiPage) {
              // إذا كان وضع الكشط متعدد الصفحات
              // تخزين البيانات وفتح صفحة النتائج
              chrome.storage.local.set({
                scrapedData: response.data,
                isMultiPageScraping: true
              }, function() {
                // التحقق من وجود صفحة نتائج مفتوحة بالفعل
                chrome.storage.local.get(['resultsTabId'], function(result) {
                  if (result.resultsTabId) {
                    // التحقق من وجود علامة التبويب
                    chrome.tabs.get(result.resultsTabId, function(tab) {
                      if (tab && !chrome.runtime.lastError) {
                        // إذا كانت علامة التبويب موجودة، قم بتحديثها
                        chrome.tabs.update(result.resultsTabId, { active: true }, function() {
                          // إرسال رسالة لتحديث البيانات
                          chrome.tabs.sendMessage(result.resultsTabId, { action: "updateData" });
                          // إغلاق النافذة المنبثقة
                          window.close();
                        });
                      } else {
                        // إذا لم تكن علامة التبويب موجودة، قم بإنشاء واحدة جديدة
                        createNewResultsTab();
                      }
                    });
                  } else {
                    // إذا لم تكن هناك علامة تبويب مخزنة، قم بإنشاء واحدة جديدة
                    createNewResultsTab();
                  }
                });
              });

              // دالة لإنشاء علامة تبويب نتائج جديدة
              function createNewResultsTab() {
                chrome.tabs.create({ url: 'results.html' }, function(tab) {
                  // تخزين معرف علامة التبويب
                  chrome.storage.local.set({ resultsTabId: tab.id });
                  // إغلاق النافذة المنبثقة
                  window.close();
                });
              }
            } else {
              // عرض رسالة نجاح
              statusDiv.textContent = 'تم كشط الجدول بنجاح وجاري تحميل الملف!';
              statusDiv.className = 'status success';
              statusDiv.style.display = 'block';
            }
          } else {
            // عرض رسالة خطأ مفصلة
            let errorMessage = 'حدث خطأ أثناء كشط الجدول';

            if (response && response.error) {
              errorMessage += ': ' + response.error;
            } else if (!response) {
              errorMessage += ': لا يوجد استجابة من الصفحة. تأكد من أن الصفحة تحتوي على جداول.';
            } else {
              errorMessage += ': خطأ غير معروف';
            }

            console.error('❌ خطأ في الكشط:', errorMessage);
            statusDiv.textContent = errorMessage;
            statusDiv.className = 'status error';
            statusDiv.style.display = 'block';
          }

          // إعادة تفعيل الزر
          scrapeButton.disabled = false;
          scrapeButton.textContent = 'كشط الجدول';
        });
      });
    } catch (error) {
      // عرض رسالة خطأ
      statusDiv.textContent = 'حدث خطأ: ' + error.message;
      statusDiv.className = 'status error';
      statusDiv.style.display = 'block';

      // إعادة تفعيل الزر
      scrapeButton.disabled = false;
      scrapeButton.textContent = 'كشط الجدول';
    }
  });

  // زر فتح صفحة النتائج
  openResultsButton.addEventListener('click', function() {
    console.log('🔄 فتح صفحة النتائج...');

    // التحقق من وجود صفحة نتائج مفتوحة بالفعل
    chrome.storage.local.get(['resultsTabId'], function(result) {
      if (result.resultsTabId) {
        // التحقق من وجود علامة التبويب
        chrome.tabs.get(result.resultsTabId, function(tab) {
          if (tab && !chrome.runtime.lastError) {
            // إذا كانت علامة التبويب موجودة، قم بتفعيلها
            console.log('✅ تم العثور على صفحة النتائج، جاري التفعيل...');
            chrome.tabs.update(result.resultsTabId, { active: true }, function() {
              // إغلاق النافذة المنبثقة
              window.close();
            });
          } else {
            // إذا لم تكن علامة التبويب موجودة، قم بإنشاء واحدة جديدة
            console.log('⚠️ صفحة النتائج غير موجودة، جاري إنشاء صفحة جديدة...');
            createNewResultsTab();
          }
        });
      } else {
        // إذا لم تكن هناك علامة تبويب مخزنة، قم بإنشاء واحدة جديدة
        console.log('📄 إنشاء صفحة نتائج جديدة...');
        createNewResultsTab();
      }
    });

    // دالة لإنشاء علامة تبويب نتائج جديدة
    function createNewResultsTab() {
      chrome.tabs.create({ url: 'results.html' }, function(tab) {
        if (chrome.runtime.lastError) {
          console.error('❌ خطأ في إنشاء صفحة النتائج:', chrome.runtime.lastError);
          statusDiv.textContent = 'خطأ في فتح صفحة النتائج: ' + chrome.runtime.lastError.message;
          statusDiv.className = 'status error';
          statusDiv.style.display = 'block';
          return;
        }

        // تخزين معرف علامة التبويب
        chrome.storage.local.set({ resultsTabId: tab.id });
        console.log('✅ تم إنشاء صفحة النتائج بنجاح');
        // إغلاق النافذة المنبثقة
        window.close();
      });
    }
  });

  // زر الكشط التلقائي لجميع الصفحات
  autoScrapeAllButton.addEventListener('click', async function() {
    if (autoScrapeInProgress) {
      return; // منع النقر المتعدد
    }

    try {
      // تغيير حالة الزر والواجهة
      autoScrapeInProgress = true;
      autoScrapeAllButton.disabled = true;
      autoScrapeAllButton.textContent = 'جاري البحث عن الصفحات...';
      autoScrapeProgress.style.display = 'block';
      autoScrapeControls.style.display = 'block';

      // الحصول على علامة التبويب النشطة
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // التأكد من تحميل content script
      await ensureContentScriptLoaded(tab.id);

      // تحديد الخيارات
      const options = {
        selectionMode: autoSelector.checked ? 'auto' : 'manual',
        exportFormat: 'csv',
        multiPage: true, // دائماً متعدد الصفحات للكشط التلقائي
        tableType: getSelectedTableType(),
        autoScrapeMode: true // علامة للكشط التلقائي
      };

      // دالة للحصول على نوع الجدول المحدد
      function getSelectedTableType() {
        if (tableTypeTraditional.checked) return 'traditional';
        if (tableTypeResponsive.checked) return 'responsive';
        return 'auto'; // القيمة الافتراضية
      }

      // بدء عملية الكشط التلقائي
      chrome.tabs.sendMessage(tab.id, {
        action: "startAutoScrape",
        options: options
      }, function(response) {
        if (chrome.runtime.lastError) {
          handleAutoScrapeError('فشل في الاتصال مع الصفحة: ' + chrome.runtime.lastError.message);
          return;
        }

        if (response && response.success) {
          // تحديث معلومات التقدم
          autoScrapeTotalPages = response.totalPages || 1;
          autoScrapeCurrentPage = response.currentPage || 1;

          console.log(`🚀 بدء الكشط التلقائي: ${autoScrapeTotalPages} صفحة`);
          updateProgressDisplay();

          // بدء مراقبة التقدم
          monitorAutoScrapeProgress(tab.id);
        } else {
          // عرض رسالة خطأ
          handleAutoScrapeError(response ? response.error : 'لا يوجد استجابة من الصفحة');
        }
      });
    } catch (error) {
      handleAutoScrapeError(error.message);
    }
  });

  // زر إيقاف مؤقت للكشط التلقائي
  pauseAutoScrape.addEventListener('click', function() {
    if (autoScrapePaused) {
      // استئناف الكشط
      autoScrapePaused = false;
      pauseAutoScrape.textContent = 'إيقاف مؤقت';
      chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (tabs.length > 0) {
          chrome.tabs.sendMessage(tabs[0].id, { action: "resumeAutoScrape" });
        }
      });
    } else {
      // إيقاف مؤقت للكشط
      autoScrapePaused = true;
      pauseAutoScrape.textContent = 'استئناف';
      chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        if (tabs.length > 0) {
          chrome.tabs.sendMessage(tabs[0].id, { action: "pauseAutoScrape" });
        }
      });
    }
  });

  // زر إيقاف الكشط التلقائي
  stopAutoScrape.addEventListener('click', function() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, { action: "stopAutoScrape" });
      }
    });
    resetAutoScrapeUI();
  });

  // دالة لمراقبة تقدم الكشط التلقائي
  function monitorAutoScrapeProgress(tabId) {
    let progressCheckCount = 0;
    const maxChecks = 300; // 5 دقائق كحد أقصى

    const progressInterval = setInterval(() => {
      progressCheckCount++;

      if (!autoScrapeInProgress || progressCheckCount > maxChecks) {
        clearInterval(progressInterval);
        if (progressCheckCount > maxChecks) {
          handleAutoScrapeError('انتهت مهلة الكشط التلقائي');
        }
        return;
      }

      chrome.tabs.sendMessage(tabId, { action: "getAutoScrapeProgress" }, function(response) {
        if (chrome.runtime.lastError) {
          console.warn('خطأ في الاتصال:', chrome.runtime.lastError);
          return;
        }

        if (response && response.success) {
          autoScrapeCurrentPage = response.currentPage || autoScrapeCurrentPage;
          autoScrapeTotalPages = response.totalPages || autoScrapeTotalPages;

          updateProgressDisplay();

          // التحقق من اكتمال العملية
          if (response.completed) {
            clearInterval(progressInterval);
            handleAutoScrapeComplete(response.data);
          }
        } else if (response && response.error) {
          clearInterval(progressInterval);
          handleAutoScrapeError(response.error);
        }
      });
    }, 1000); // فحص كل ثانية

    // حفظ مرجع للـ interval لإمكانية إيقافه لاحقاً
    window.autoScrapeProgressInterval = progressInterval;
  }

  // دالة لتحديث عرض التقدم
  function updateProgressDisplay() {
    const progress = autoScrapeTotalPages > 0 ? (autoScrapeCurrentPage / autoScrapeTotalPages) * 100 : 0;

    // تحديث شريط التقدم مع أنيميشن
    progressFill.style.width = Math.min(progress, 100) + '%';

    // تغيير لون الشريط حسب التقدم
    if (progress < 30) {
      progressFill.style.background = 'linear-gradient(90deg, #ff9800, #ffc107)';
    } else if (progress < 70) {
      progressFill.style.background = 'linear-gradient(90deg, #2196F3, #03a9f4)';
    } else {
      progressFill.style.background = 'linear-gradient(90deg, #4CAF50, #8bc34a)';
    }

    // تحديث النص
    if (autoScrapePaused) {
      progressText.textContent = `⏸️ متوقف مؤقتاً - الصفحة ${autoScrapeCurrentPage} من ${autoScrapeTotalPages}`;
      progressText.style.color = '#ff9800';
    } else {
      progressText.textContent = `🔄 جاري كشط الصفحة ${autoScrapeCurrentPage} من ${autoScrapeTotalPages}... (${Math.round(progress)}%)`;
      progressText.style.color = '#333';
    }

    // إضافة تأثير نبضة عند التقدم
    if (!autoScrapePaused && progress > 0) {
      progressFill.style.boxShadow = '0 0 10px rgba(33, 150, 243, 0.5)';
      setTimeout(() => {
        progressFill.style.boxShadow = 'none';
      }, 500);
    }
  }

  // دالة لمعالجة اكتمال الكشط التلقائي
  function handleAutoScrapeComplete(data) {
    // تخزين البيانات وفتح صفحة النتائج
    chrome.storage.local.set({
      scrapedData: data,
      isMultiPageScraping: true,
      autoScrapeCompleted: true
    }, function() {
      // فتح صفحة النتائج
      chrome.storage.local.get(['resultsTabId'], function(result) {
        if (result.resultsTabId) {
          chrome.tabs.get(result.resultsTabId, function(tab) {
            if (tab && !chrome.runtime.lastError) {
              chrome.tabs.update(result.resultsTabId, { active: true }, function() {
                chrome.tabs.sendMessage(result.resultsTabId, {
                  action: "updateData",
                  autoScrapeComplete: true
                });
                window.close();
              });
            } else {
              createNewResultsTab();
            }
          });
        } else {
          createNewResultsTab();
        }
      });
    });

    // تحديث الواجهة
    progressText.textContent = `🎉 تم الانتهاء! تم كشط ${autoScrapeTotalPages} صفحة بنجاح`;
    progressFill.style.width = '100%';
    progressFill.style.background = 'linear-gradient(90deg, #4CAF50, #2196F3)';

    // إظهار رسالة نجاح
    statusDiv.textContent = `تم كشط ${autoScrapeTotalPages} صفحة بنجاح! جاري فتح صفحة النتائج...`;
    statusDiv.className = 'status success';
    statusDiv.style.display = 'block';

    setTimeout(() => {
      resetAutoScrapeUI();
    }, 3000);

    function createNewResultsTab() {
      chrome.tabs.create({ url: 'results.html' }, function(tab) {
        chrome.storage.local.set({ resultsTabId: tab.id });
        window.close();
      });
    }
  }

  // دالة لمعالجة أخطاء الكشط التلقائي
  function handleAutoScrapeError(errorMessage) {
    console.error('❌ خطأ في الكشط التلقائي:', errorMessage);

    // تحديث شريط التقدم ليعكس الخطأ
    progressText.textContent = '❌ حدث خطأ: ' + errorMessage;
    progressText.style.color = '#f44336';
    progressFill.style.background = '#f44336';
    progressFill.style.width = '100%';

    // إظهار رسالة خطأ في الـ status
    statusDiv.textContent = 'فشل الكشط التلقائي: ' + errorMessage;
    statusDiv.className = 'status error';
    statusDiv.style.display = 'block';

    // تغيير لون الزر ليعكس الخطأ
    autoScrapeAllButton.style.background = '#f44336';
    autoScrapeAllButton.textContent = 'حدث خطأ - انقر للمحاولة مرة أخرى';
    autoScrapeAllButton.disabled = false;

    // إخفاء أزرار التحكم
    autoScrapeControls.style.display = 'none';

    // إعادة تعيين الواجهة بعد 5 ثوان
    setTimeout(() => {
      resetAutoScrapeUI();
      statusDiv.style.display = 'none';
    }, 5000);
  }

  // دالة لإعادة تعيين واجهة الكشط التلقائي
  function resetAutoScrapeUI() {
    // إيقاف أي intervals جارية
    if (window.autoScrapeProgressInterval) {
      clearInterval(window.autoScrapeProgressInterval);
      window.autoScrapeProgressInterval = null;
    }

    autoScrapeInProgress = false;
    autoScrapePaused = false;
    autoScrapeCurrentPage = 1;
    autoScrapeTotalPages = 0;

    // إعادة تعيين الزر الرئيسي
    autoScrapeAllButton.disabled = false;
    autoScrapeAllButton.textContent = 'كشط تلقائي لجميع الصفحات';
    autoScrapeAllButton.style.background = '#2196F3';

    // إخفاء عناصر التقدم مع أنيميشن
    autoScrapeProgress.style.opacity = '0';
    autoScrapeControls.style.opacity = '0';

    setTimeout(() => {
      autoScrapeProgress.style.display = 'none';
      autoScrapeControls.style.display = 'none';
      autoScrapeProgress.style.opacity = '1';
      autoScrapeControls.style.opacity = '1';
    }, 300);

    // إعادة تعيين شريط التقدم
    progressFill.style.width = '0%';
    progressFill.style.background = 'linear-gradient(90deg, #4CAF50, #2196F3)';
    progressFill.style.boxShadow = 'none';
    progressText.textContent = 'جاري البحث عن الصفحات...';
    progressText.style.color = '#333';
    pauseAutoScrape.textContent = 'إيقاف مؤقت';

    // إعادة فحص إمكانية الكشط التلقائي
    setTimeout(() => {
      checkAutoScrapeAvailability();
    }, 500);
  }

  // دالة للتأكد من تحميل content script
  async function ensureContentScriptLoaded(tabId) {
    try {
      // محاولة إرسال رسالة ping للتحقق من وجود content script
      return new Promise((resolve, reject) => {
        chrome.tabs.sendMessage(tabId, { action: "ping" }, function(response) {
          if (chrome.runtime.lastError) {
            console.log('🔄 content script غير محمل، جاري التحميل...');

            // تحميل content script يدوياً
            chrome.scripting.executeScript({
              target: { tabId: tabId },
              files: ['content.js']
            }, function() {
              if (chrome.runtime.lastError) {
                console.error('❌ فشل في تحميل content script:', chrome.runtime.lastError);
                reject(new Error('فشل في تحميل content script: ' + chrome.runtime.lastError.message));
              } else {
                console.log('✅ تم تحميل content script بنجاح');
                resolve();
              }
            });
          } else {
            console.log('✅ content script محمل بالفعل');
            resolve();
          }
        });
      });
    } catch (error) {
      console.error('❌ خطأ في التحقق من content script:', error);
      throw error;
    }
  }

  // دالة لفحص إمكانية الكشط التلقائي
  async function checkAutoScrapeAvailability() {
    console.log('🔍 فحص إمكانية الكشط التلقائي...');

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      console.log('📋 علامة التبويب النشطة:', tab.url);

      const autoScrapeSection = document.querySelector('.auto-scrape-section');

      // إظهار القسم مؤقتاً للاختبار
      console.log('🔄 إظهار قسم الكشط التلقائي مؤقتاً...');
      autoScrapeSection.style.display = 'block';
      autoScrapeSection.classList.add('loading');

      try {
        // التأكد من تحميل content script أولاً
        console.log('🔄 التحقق من تحميل content script...');
        await ensureContentScriptLoaded(tab.id);
        console.log('✅ content script محمل');

        // إرسال رسالة للتحقق من وجود عناصر التنقل
        console.log('🔄 فحص عناصر التنقل...');
        chrome.tabs.sendMessage(tab.id, { action: "checkPagination" }, function(response) {
          // إزالة تأثير التحميل
          autoScrapeSection.classList.remove('loading');

          if (chrome.runtime.lastError) {
            console.warn('❌ خطأ في الاتصال:', chrome.runtime.lastError);
            hideAutoScrapeSection();
            return;
          }

          console.log('📊 استجابة فحص التنقل:', response);

          if (response && response.success && response.hasPagination && response.totalPages > 1) {
            // إظهار قسم الكشط التلقائي مع أنيميشن
            console.log(`✅ تم اكتشاف ${response.totalPages} صفحة - إظهار القسم`);
            showAutoScrapeSection();

            // تحديث نص الزر ليعكس عدد الصفحات
            autoScrapeAllButton.textContent = `🚀 كشط تلقائي لجميع الصفحات (${response.totalPages} صفحة)`;

            // إضافة معلومات إضافية
            const infoText = document.createElement('div');
            infoText.style.cssText = `
              font-size: 12px;
              color: #666;
              text-align: center;
              margin-top: 5px;
            `;
            infoText.textContent = `تم اكتشاف ${response.totalPages} صفحة - النوع: ${getPaginationTypeText(response.paginationType)}`;

            // إزالة النص القديم إذا وجد
            const oldInfo = autoScrapeSection.querySelector('.pagination-info');
            if (oldInfo) {
              oldInfo.remove();
            }

            infoText.className = 'pagination-info';
            autoScrapeAllButton.parentNode.insertBefore(infoText, autoScrapeAllButton.nextSibling);

            console.log(`✅ تم اكتشاف ${response.totalPages} صفحة من النوع ${response.paginationType}`);
          } else {
            // إخفاء قسم الكشط التلقائي إذا لم توجد عناصر تنقل
            console.log('❌ لم يتم العثور على صفحات متعددة أو فشل الفحص');
            console.log('📊 تفاصيل الاستجابة:', {
              responseExists: !!response,
              success: response?.success,
              hasPagination: response?.hasPagination,
              totalPages: response?.totalPages
            });

            // للاختبار: إظهار القسم حتى لو لم توجد صفحات متعددة
            console.log('🧪 إظهار القسم للاختبار...');
            showAutoScrapeSection();
            autoScrapeAllButton.textContent = '🧪 كشط تلقائي (وضع الاختبار)';
          }
        });
      } catch (contentScriptError) {
        console.error('❌ خطأ في تحميل content script:', contentScriptError);
        autoScrapeSection.classList.remove('loading');

        // للاختبار: إظهار القسم حتى لو فشل تحميل content script
        console.log('🧪 إظهار القسم للاختبار رغم فشل content script...');
        showAutoScrapeSection();
        autoScrapeAllButton.textContent = '⚠️ كشط تلقائي (خطأ في التحميل)';
      }
    } catch (error) {
      console.error('❌ خطأ عام في فحص الكشط التلقائي:', error);

      // للاختبار: إظهار القسم حتى لو حدث خطأ
      const autoScrapeSection = document.querySelector('.auto-scrape-section');
      console.log('🧪 إظهار القسم للاختبار رغم الخطأ...');
      showAutoScrapeSection();
      autoScrapeAllButton.textContent = '❌ كشط تلقائي (خطأ عام)';
    }
  }

  // دالة لإظهار قسم الكشط التلقائي
  function showAutoScrapeSection() {
    console.log('🔄 محاولة إظهار قسم الكشط التلقائي...');
    const autoScrapeSection = document.querySelector('.auto-scrape-section');

    if (!autoScrapeSection) {
      console.error('❌ لم يتم العثور على عنصر .auto-scrape-section');
      return;
    }

    console.log('✅ تم العثور على قسم الكشط التلقائي');
    autoScrapeSection.style.display = 'block';
    autoScrapeSection.classList.remove('hide');

    console.log('✅ تم إظهار قسم الكشط التلقائي بنجاح');

    // إضافة تأثير بصري
    setTimeout(() => {
      autoScrapeSection.style.transform = 'scale(1.02)';
      setTimeout(() => {
        autoScrapeSection.style.transform = 'scale(1)';
      }, 200);
    }, 100);
  }

  // دالة لإخفاء قسم الكشط التلقائي
  function hideAutoScrapeSection() {
    console.log('🔄 محاولة إخفاء قسم الكشط التلقائي...');
    const autoScrapeSection = document.querySelector('.auto-scrape-section');

    if (!autoScrapeSection) {
      console.error('❌ لم يتم العثور على عنصر .auto-scrape-section');
      return;
    }

    console.log('✅ إخفاء قسم الكشط التلقائي');
    autoScrapeSection.classList.add('hide');

    setTimeout(() => {
      autoScrapeSection.style.display = 'none';
      autoScrapeSection.classList.remove('hide');
      console.log('✅ تم إخفاء قسم الكشط التلقائي بنجاح');
    }, 300);
  }

  // دالة لتحويل نوع التنقل إلى نص مفهوم
  function getPaginationTypeText(type) {
    const types = {
      'data-page': 'Data Page',
      'standard': 'قياسي',
      'href-based': 'رابط',
      'onclick-based': 'JavaScript',
      'text-based': 'نصي'
    };
    return types[type] || 'غير محدد';
  }

  // استماع للرسائل من background.js
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "autoScrapeError") {
      handleAutoScrapeError(request.error);
    } else if (request.action === "progressUpdate") {
      // تحديث التقدم من background.js
      autoScrapeCurrentPage = request.currentPage;
      autoScrapeTotalPages = request.totalPages;
      updateProgressDisplay();
      console.log(`📊 تم استلام تحديث التقدم: ${request.currentPage}/${request.totalPages}`);
    }
    return true;
  });

  // تهيئة حالة الأزرار عند بدء التشغيل
  updateButtonsState();
});
